{"name": "lapxpert-frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*", "format": "prettier --write src/"}, "dependencies": {"@primeuix/themes": "^1.0.0", "@tailwindcss/postcss": "^4.0.17", "@tailwindcss/vite": "^4.0.17", "axios": "^1.8.4", "chart.js": "^4.4.8", "date-fns": "^4.1.0", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "lodash-es": "^4.17.21", "pinia": "^3.0.1", "primeicons": "^7.0.0", "primevue": "^4.3.3", "tailwindcss-primeui": "^0.6.1", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-qrcode-reader": "^5.7.1", "vue-router": "^4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.23.0", "@iconify/json": "^2.2.321", "@iconify/tailwind": "^1.2.0", "@primevue/auto-import-resolver": "^4.3.3", "@rushstack/eslint-patch": "^1.11.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/babel-plugin-jsx": "^1.4.0", "@vue/eslint-config-prettier": "^10.2.0", "autoprefixer": "^10.4.21", "eslint": "^9.23.0", "eslint-plugin-oxlint": "^0.16.3", "eslint-plugin-vue": "~10.0.0", "globals": "^16.0.0", "npm-run-all2": "^7.0.2", "oxlint": "^0.16.3", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "prettier": "3.5.3", "sass": "^1.86.0", "tailwindcss": "~3.4.17", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.3", "vite-plugin-vue-devtools": "^7.7.2"}}