plugins {
	id 'java'
	id 'org.springframework.boot' version '3.4.4'
	id 'io.spring.dependency-management' version '1.1.7'
}

group = 'com.lapxpert'
version = '0.0.1-SNAPSHOT'

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom annotationProcessor
	}
}

repositories {
	mavenCentral()
}

dependencies {
	implementation 'org.springframework.boot:spring-boot-starter-actuator'
	implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
	implementation 'org.springframework.boot:spring-boot-starter-data-rest'
	implementation 'org.springframework.boot:spring-boot-starter-web'
	implementation 'org.springframework.boot:spring-boot-starter-security'
	implementation 'org.springframework.boot:spring-boot-starter-cache'
	implementation 'io.jsonwebtoken:jjwt:0.9.1'
	implementation 'org.springframework.boot:spring-boot-starter-data-redis'
	implementation 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
	implementation 'org.liquibase:liquibase-core'
	implementation 'org.springframework.boot:spring-boot-starter-batch'
	implementation 'org.springframework.boot:spring-boot-starter-validation'
	testImplementation 'org.springframework.batch:spring-batch-test'
	compileOnly 'org.projectlombok:lombok'
	developmentOnly 'org.springframework.boot:spring-boot-devtools'
	runtimeOnly 'org.postgresql:postgresql'
	annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
	annotationProcessor 'org.projectlombok:lombok'
	testImplementation 'org.springframework.boot:spring-boot-starter-test'
	testRuntimeOnly 'org.junit.platform:junit-platform-launcher'
	implementation 'org.mapstruct:mapstruct:+'
	annotationProcessor 'org.mapstruct:mapstruct-processor:+'
	implementation 'io.hypersistence:hypersistence-utils-hibernate-60:3.9.4'
	implementation 'org.springframework.boot:spring-boot-starter-mail:3.4.4'
	implementation 'io.minio:minio:8.5.17'
}

tasks.named('test') {
	useJUnitPlatform()

	// Suppress dynamic agent loading warnings for Mockito compatibility
	jvmArgs '-XX:+EnableDynamicAgentLoading'
}
