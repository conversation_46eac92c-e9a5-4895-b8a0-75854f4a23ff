package com.lapxpert.backend.nguoidung.application.mapper;

import com.lapxpert.backend.nguoidung.application.dto.KhachHangDTO;
import com.lapxpert.backend.nguoidung.application.dto.NhanVienDTO;
import com.lapxpert.backend.nguoidung.domain.entity.NguoiDung;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

/**
 * MapStruct mapper for NguoiDung entity and DTOs
 * Provides consistent mapping between entity and different DTO types
 */
@Mapper(componentModel = "spring", uses = {DiaChiMapper.class})
public interface NguoiDungMapper {

    /**
     * Convert NguoiDung entity to KhachHangDTO
     * Excludes CCCD field as customers don't provide identity numbers
     */
    @Mapping(target = "diaChis", source = "diaChis")
    KhachHangDTO toKhachHangDto(NguoiDung nguoiDung);

    /**
     * Convert NguoiDung entity to NhanVienDTO
     * Includes CCCD field as staff are required to provide identity numbers
     */
    @Mapping(target = "diaChis", source = "diaChis")
    NhanVienDTO toNhanVienDto(NguoiDung nguoiDung);

    /**
     * Convert KhachHangDTO to NguoiDung entity
     * Ignores audit fields that should be managed by the audit system
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "maNguoiDung", ignore = true) // Generated by service
    @Mapping(target = "matKhau", ignore = true) // Handled separately for security
    @Mapping(target = "vaiTro", ignore = true) // Set by service based on DTO type
    @Mapping(target = "cccd", ignore = true) // Customers don't have CCCD
    @Mapping(target = "hoaDonsAsCustomer", ignore = true)
    @Mapping(target = "hoaDonsAsStaff", ignore = true)
    @Mapping(target = "phieuGiamGias", ignore = true)
    @Mapping(target = "gioHang", ignore = true)
    @Mapping(target = "danhSachYeuThichs", ignore = true)
    @Mapping(target = "danhGias", ignore = true)
    @Mapping(target = "diaChis", ignore = true) // Handled separately
    NguoiDung toEntityFromKhachHang(KhachHangDTO khachHangDTO);

    /**
     * Convert NhanVienDTO to NguoiDung entity
     * Includes CCCD field for staff members
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "maNguoiDung", ignore = true) // Generated by service
    @Mapping(target = "matKhau", ignore = true) // Handled separately for security
    @Mapping(target = "hoaDonsAsCustomer", ignore = true)
    @Mapping(target = "hoaDonsAsStaff", ignore = true)
    @Mapping(target = "phieuGiamGias", ignore = true)
    @Mapping(target = "gioHang", ignore = true)
    @Mapping(target = "danhSachYeuThichs", ignore = true)
    @Mapping(target = "danhGias", ignore = true)
    @Mapping(target = "diaChis", ignore = true) // Handled separately
    NguoiDung toEntityFromNhanVien(NhanVienDTO nhanVienDTO);

    /**
     * Update existing NguoiDung entity from KhachHangDTO
     * Preserves security-critical fields
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "maNguoiDung", ignore = true)
    @Mapping(target = "matKhau", ignore = true)
    @Mapping(target = "vaiTro", ignore = true)
    @Mapping(target = "cccd", ignore = true) // Customers don't have CCCD
    @Mapping(target = "ngayTao", ignore = true)
    @Mapping(target = "ngayCapNhat", ignore = true)
    @Mapping(target = "nguoiTao", ignore = true)
    @Mapping(target = "nguoiCapNhat", ignore = true)
    @Mapping(target = "hoaDonsAsCustomer", ignore = true)
    @Mapping(target = "hoaDonsAsStaff", ignore = true)
    @Mapping(target = "phieuGiamGias", ignore = true)
    @Mapping(target = "gioHang", ignore = true)
    @Mapping(target = "danhSachYeuThichs", ignore = true)
    @Mapping(target = "danhGias", ignore = true)
    @Mapping(target = "diaChis", ignore = true) // Handled separately
    void updateEntityFromKhachHang(KhachHangDTO khachHangDTO, @MappingTarget NguoiDung nguoiDung);

    /**
     * Update existing NguoiDung entity from NhanVienDTO
     * Includes CCCD field for staff members
     */
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "maNguoiDung", ignore = true)
    @Mapping(target = "matKhau", ignore = true)
    @Mapping(target = "ngayTao", ignore = true)
    @Mapping(target = "ngayCapNhat", ignore = true)
    @Mapping(target = "nguoiTao", ignore = true)
    @Mapping(target = "nguoiCapNhat", ignore = true)
    @Mapping(target = "hoaDonsAsCustomer", ignore = true)
    @Mapping(target = "hoaDonsAsStaff", ignore = true)
    @Mapping(target = "phieuGiamGias", ignore = true)
    @Mapping(target = "gioHang", ignore = true)
    @Mapping(target = "danhSachYeuThichs", ignore = true)
    @Mapping(target = "danhGias", ignore = true)
    @Mapping(target = "diaChis", ignore = true) // Handled separately
    void updateEntityFromNhanVien(NhanVienDTO nhanVienDTO, @MappingTarget NguoiDung nguoiDung);

    /**
     * Convert list of NguoiDung entities to KhachHangDTO list
     */
    List<KhachHangDTO> toKhachHangDtoList(List<NguoiDung> nguoiDungs);

    /**
     * Convert list of NguoiDung entities to NhanVienDTO list
     */
    List<NhanVienDTO> toNhanVienDtoList(List<NguoiDung> nguoiDungs);
}
