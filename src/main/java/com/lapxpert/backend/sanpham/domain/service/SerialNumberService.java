package com.lapxpert.backend.sanpham.domain.service;

import com.lapxpert.backend.sanpham.domain.entity.SerialNumber;
import com.lapxpert.backend.sanpham.domain.entity.SerialNumberAuditHistory;
import com.lapxpert.backend.sanpham.domain.entity.sanpham.SanPhamChiTiet;
import com.lapxpert.backend.sanpham.domain.enums.TrangThaiSerialNumber;
import com.lapxpert.backend.sanpham.domain.repository.SerialNumberAuditHistoryRepository;
import com.lapxpert.backend.sanpham.domain.repository.SerialNumberRepository;
import com.lapxpert.backend.sanpham.domain.repository.SanPhamChiTietRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for managing serial numbers and inventory tracking.
 * Provides comprehensive serial number lifecycle management.
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional
public class SerialNumberService {

    private final SerialNumberRepository serialNumberRepository;
    private final SerialNumberAuditHistoryRepository auditHistoryRepository;
    private final SanPhamChiTietRepository sanPhamChiTietRepository;

    // Serial Number CRUD Operations

    /**
     * Create a new serial number
     */
    public SerialNumber createSerialNumber(SerialNumber serialNumber, String user, String reason) {
        // Validate serial number doesn't exist
        if (serialNumberRepository.existsBySerialNumberValue(serialNumber.getSerialNumberValue())) {
            throw new IllegalArgumentException("Serial number already exists: " + serialNumber.getSerialNumberValue());
        }

        // Set default status if not provided
        if (serialNumber.getTrangThai() == null) {
            serialNumber.setTrangThai(TrangThaiSerialNumber.AVAILABLE);
        }

        // Save serial number
        SerialNumber savedSerialNumber = serialNumberRepository.save(serialNumber);

        // Create audit trail
        SerialNumberAuditHistory auditEntry = SerialNumberAuditHistory.createEntry(
            savedSerialNumber.getId(),
            buildAuditJson(savedSerialNumber),
            user,
            reason != null ? reason : "Tạo serial number mới"
        );
        auditHistoryRepository.save(auditEntry);

        log.info("Created serial number: {} for variant: {}", 
                savedSerialNumber.getSerialNumberValue(), 
                savedSerialNumber.getSanPhamChiTiet().getId());

        return savedSerialNumber;
    }

    /**
     * Update serial number
     */
    public SerialNumber updateSerialNumber(Long id, SerialNumber updatedSerialNumber, String user, String reason) {
        SerialNumber existingSerialNumber = serialNumberRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Serial number not found"));

        String oldValues = buildAuditJson(existingSerialNumber);

        // Update fields
        existingSerialNumber.setBatchNumber(updatedSerialNumber.getBatchNumber());
        existingSerialNumber.setNgaySanXuat(updatedSerialNumber.getNgaySanXuat());
        existingSerialNumber.setNgayHetBaoHanh(updatedSerialNumber.getNgayHetBaoHanh());
        existingSerialNumber.setNhaCungCap(updatedSerialNumber.getNhaCungCap());
        existingSerialNumber.setGhiChu(updatedSerialNumber.getGhiChu());

        SerialNumber savedSerialNumber = serialNumberRepository.save(existingSerialNumber);

        // Create audit trail
        SerialNumberAuditHistory auditEntry = SerialNumberAuditHistory.updateEntry(
            savedSerialNumber.getId(),
            oldValues,
            buildAuditJson(savedSerialNumber),
            user,
            reason != null ? reason : "Cập nhật thông tin serial number"
        );
        auditHistoryRepository.save(auditEntry);

        return savedSerialNumber;
    }

    /**
     * Change serial number status
     */
    public SerialNumber changeStatus(Long id, TrangThaiSerialNumber newStatus, String user, String reason) {
        SerialNumber serialNumber = serialNumberRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Serial number not found"));

        TrangThaiSerialNumber oldStatus = serialNumber.getTrangThai();
        
        // Validate status transition
        validateStatusTransition(oldStatus, newStatus);

        serialNumber.setTrangThai(newStatus);
        SerialNumber savedSerialNumber = serialNumberRepository.save(serialNumber);

        // Create audit trail
        SerialNumberAuditHistory auditEntry = SerialNumberAuditHistory.statusChangeEntry(
            savedSerialNumber.getId(),
            oldStatus.name(),
            newStatus.name(),
            user,
            reason != null ? reason : "Thay đổi trạng thái serial number"
        );
        auditHistoryRepository.save(auditEntry);

        log.info("Changed status of serial number {} from {} to {}", 
                serialNumber.getSerialNumberValue(), oldStatus, newStatus);

        return savedSerialNumber;
    }

    // Inventory Management

    /**
     * Get available quantity for a product variant
     */
    @Transactional(readOnly = true)
    public int getAvailableQuantityByVariant(Long variantId) {
        return serialNumberRepository.countAvailableByVariant(variantId);
    }

    /**
     * Reserve serial numbers for an order
     */
    public List<SerialNumber> reserveSerialNumbers(Long variantId, int quantity, String channel, String orderId, String user) {
        List<SerialNumber> availableSerialNumbers = serialNumberRepository.findAvailableByVariant(
            variantId, PageRequest.of(0, quantity)
        );

        if (availableSerialNumbers.size() < quantity) {
            throw new IllegalArgumentException(
                String.format("Insufficient inventory. Requested: %d, Available: %d", 
                             quantity, availableSerialNumbers.size())
            );
        }

        List<SerialNumber> reservedSerialNumbers = new ArrayList<>();
        
        for (int i = 0; i < quantity; i++) {
            SerialNumber serialNumber = availableSerialNumbers.get(i);
            serialNumber.reserveWithTracking(channel, orderId);
            SerialNumber savedSerialNumber = serialNumberRepository.save(serialNumber);
            reservedSerialNumbers.add(savedSerialNumber);

            // Create audit trail
            SerialNumberAuditHistory auditEntry = SerialNumberAuditHistory.reservationEntry(
                savedSerialNumber.getId(),
                channel,
                orderId,
                user,
                "Đặt trước serial number cho đơn hàng"
            );
            auditHistoryRepository.save(auditEntry);

            log.debug("Reserved serial number {} for order {} via channel {}", 
                     serialNumber.getSerialNumberValue(), orderId, channel);
        }

        log.info("Reserved {} serial numbers for order {} via channel {}", 
                quantity, orderId, channel);

        return reservedSerialNumbers;
    }

    /**
     * Confirm sale of reserved serial numbers
     */
    public void confirmSale(List<Long> serialNumberIds, String orderId, String user) {
        for (Long serialNumberId : serialNumberIds) {
            SerialNumber serialNumber = serialNumberRepository.findById(serialNumberId)
                    .orElseThrow(() -> new RuntimeException("Serial number not found: " + serialNumberId));

            serialNumber.markAsSold();
            serialNumberRepository.save(serialNumber);

            // Create audit trail
            SerialNumberAuditHistory auditEntry = SerialNumberAuditHistory.saleEntry(
                serialNumber.getId(),
                orderId,
                user,
                "Xác nhận bán serial number"
            );
            auditHistoryRepository.save(auditEntry);

            log.debug("Confirmed sale of serial number {}", serialNumber.getSerialNumberValue());
        }

        log.info("Confirmed sale of {} serial numbers for order {}", serialNumberIds.size(), orderId);
    }

    /**
     * Release reservations
     */
    public void releaseReservations(List<Long> serialNumberIds, String user, String reason) {
        for (Long serialNumberId : serialNumberIds) {
            SerialNumber serialNumber = serialNumberRepository.findById(serialNumberId)
                    .orElseThrow(() -> new RuntimeException("Serial number not found: " + serialNumberId));

            if (serialNumber.isReserved()) {
                serialNumber.releaseReservation();
                serialNumberRepository.save(serialNumber);

                // Create audit trail
                SerialNumberAuditHistory auditEntry = SerialNumberAuditHistory.releaseEntry(
                    serialNumber.getId(),
                    user,
                    reason != null ? reason : "Hủy đặt trước serial number"
                );
                auditHistoryRepository.save(auditEntry);

                log.debug("Released reservation for serial number {}", serialNumber.getSerialNumberValue());
            }
        }

        log.info("Released reservations for {} serial numbers", serialNumberIds.size());
    }

    // Bulk Operations

    /**
     * Generate serial numbers for a product variant
     */
    public List<SerialNumber> generateSerialNumbers(Long variantId, int quantity, String pattern, String user) {
        SanPhamChiTiet variant = sanPhamChiTietRepository.findById(variantId)
                .orElseThrow(() -> new RuntimeException("Product variant not found"));

        List<SerialNumber> generatedSerialNumbers = new ArrayList<>();
        String batchId = "BATCH-" + System.currentTimeMillis();

        for (int i = 1; i <= quantity; i++) {
            String serialNumberValue = generateSerialNumberValue(pattern, i);
            
            // Check if serial number already exists
            if (serialNumberRepository.existsBySerialNumberValue(serialNumberValue)) {
                log.warn("Serial number {} already exists, skipping", serialNumberValue);
                continue;
            }

            SerialNumber serialNumber = SerialNumber.builder()
                    .serialNumberValue(serialNumberValue)
                    .sanPhamChiTiet(variant)
                    .trangThai(TrangThaiSerialNumber.AVAILABLE)
                    .importBatchId(batchId)
                    .build();

            SerialNumber savedSerialNumber = serialNumberRepository.save(serialNumber);
            generatedSerialNumbers.add(savedSerialNumber);

            // Create audit trail
            SerialNumberAuditHistory auditEntry = SerialNumberAuditHistory.bulkOperationEntry(
                savedSerialNumber.getId(),
                "GENERATE",
                batchId,
                user,
                "Tạo serial number hàng loạt"
            );
            auditHistoryRepository.save(auditEntry);
        }

        log.info("Generated {} serial numbers for variant {} with batch ID {}", 
                generatedSerialNumbers.size(), variantId, batchId);

        return generatedSerialNumbers;
    }

    // Scheduled Tasks

    /**
     * Clean up expired reservations (runs every 5 minutes)
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    public void cleanupExpiredReservations() {
        Instant expiredBefore = Instant.now().minus(15, ChronoUnit.MINUTES);
        
        List<SerialNumber> expiredReservations = serialNumberRepository.findExpiredReservations(expiredBefore);
        
        if (!expiredReservations.isEmpty()) {
            int releasedCount = serialNumberRepository.releaseExpiredReservations(expiredBefore);
            
            // Create audit entries for released reservations
            for (SerialNumber serialNumber : expiredReservations) {
                SerialNumberAuditHistory auditEntry = SerialNumberAuditHistory.releaseEntry(
                    serialNumber.getId(),
                    "SYSTEM",
                    "Hết hạn đặt trước tự động"
                );
                auditHistoryRepository.save(auditEntry);
            }
            
            log.info("Released {} expired reservations", releasedCount);
        }
    }

    // Helper Methods

    private void validateStatusTransition(TrangThaiSerialNumber from, TrangThaiSerialNumber to) {
        // Define valid transitions
        Map<TrangThaiSerialNumber, Set<TrangThaiSerialNumber>> validTransitions = Map.of(
            TrangThaiSerialNumber.AVAILABLE, Set.of(TrangThaiSerialNumber.RESERVED, TrangThaiSerialNumber.DAMAGED, TrangThaiSerialNumber.UNAVAILABLE, TrangThaiSerialNumber.DISPLAY_UNIT),
            TrangThaiSerialNumber.RESERVED, Set.of(TrangThaiSerialNumber.AVAILABLE, TrangThaiSerialNumber.SOLD, TrangThaiSerialNumber.DAMAGED),
            TrangThaiSerialNumber.SOLD, Set.of(TrangThaiSerialNumber.RETURNED, TrangThaiSerialNumber.DAMAGED),
            TrangThaiSerialNumber.RETURNED, Set.of(TrangThaiSerialNumber.AVAILABLE, TrangThaiSerialNumber.DAMAGED, TrangThaiSerialNumber.DISPOSED),
            TrangThaiSerialNumber.DAMAGED, Set.of(TrangThaiSerialNumber.AVAILABLE, TrangThaiSerialNumber.DISPOSED),
            TrangThaiSerialNumber.UNAVAILABLE, Set.of(TrangThaiSerialNumber.AVAILABLE, TrangThaiSerialNumber.DAMAGED),
            TrangThaiSerialNumber.DISPLAY_UNIT, Set.of(TrangThaiSerialNumber.AVAILABLE, TrangThaiSerialNumber.DAMAGED),
            TrangThaiSerialNumber.QUALITY_CONTROL, Set.of(TrangThaiSerialNumber.AVAILABLE, TrangThaiSerialNumber.DAMAGED),
            TrangThaiSerialNumber.IN_TRANSIT, Set.of(TrangThaiSerialNumber.AVAILABLE, TrangThaiSerialNumber.QUALITY_CONTROL)
        );

        Set<TrangThaiSerialNumber> allowedTransitions = validTransitions.get(from);
        if (allowedTransitions == null || !allowedTransitions.contains(to)) {
            throw new IllegalArgumentException(
                String.format("Invalid status transition from %s to %s", from, to)
            );
        }
    }

    private String generateSerialNumberValue(String pattern, int sequence) {
        // Replace placeholders in pattern
        return pattern
                .replace("{SEQ}", String.format("%04d", sequence))
                .replace("{TIMESTAMP}", String.valueOf(System.currentTimeMillis() % 100000));
    }

    private String buildAuditJson(SerialNumber serialNumber) {
        return String.format(
            "{\"serialNumberValue\":\"%s\",\"trangThai\":\"%s\",\"variantId\":%d,\"batchNumber\":\"%s\",\"supplier\":\"%s\"}",
            serialNumber.getSerialNumberValue(),
            serialNumber.getTrangThai(),
            serialNumber.getSanPhamChiTiet().getId(),
            serialNumber.getBatchNumber() != null ? serialNumber.getBatchNumber() : "",
            serialNumber.getNhaCungCap() != null ? serialNumber.getNhaCungCap() : ""
        );
    }
}
