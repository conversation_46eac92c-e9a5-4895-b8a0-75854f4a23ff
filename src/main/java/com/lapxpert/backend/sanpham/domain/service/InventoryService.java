package com.lapxpert.backend.sanpham.domain.service;

import com.lapxpert.backend.hoadon.domain.dto.HoaDonChiTietDto;
import com.lapxpert.backend.sanpham.domain.entity.SerialNumber;
import com.lapxpert.backend.sanpham.domain.entity.sanpham.SanPhamChiTiet;
import com.lapxpert.backend.sanpham.domain.repository.SanPhamChiTietRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Service for managing product variant inventory.
 * Delegates individual unit tracking to SerialNumberService.
 * This service focuses on variant-level operations.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class InventoryService {

    private final SanPhamChiTietRepository sanPhamChiTietRepository;
    private final SerialNumberService serialNumberService;

    /**
     * Reservation timeout in minutes (configurable via application properties)
     */
    @Value("${inventory.reservation.timeout.minutes:15}")
    private long reservationTimeoutMinutes;

    /**
     * POS reservation priority timeout in minutes
     */
    @Value("${inventory.reservation.pos.timeout.minutes:30}")
    private long posReservationTimeoutMinutes;

    /**
     * Reserve product items for an order with channel tracking.
     * Delegates to SerialNumberService for individual unit tracking.
     *
     * @param orderItems List of items to reserve
     * @param channel Channel making the reservation (POS, ONLINE, etc.)
     * @param orderId Order ID for tracking
     * @param user User performing the reservation
     * @return List of reserved serial number IDs for tracking
     * @throws IllegalArgumentException if insufficient inventory
     */
    @Transactional
    public List<Long> reserveItemsWithTracking(List<HoaDonChiTietDto> orderItems, String channel, String orderId, String user) {
        List<Long> reservedSerialNumberIds = new ArrayList<>();

        try {
            for (HoaDonChiTietDto orderItem : orderItems) {
                Long productVariantId = orderItem.getSanPhamChiTietId();
                Integer quantity = orderItem.getSoLuong();

                // Delegate to SerialNumberService for individual unit reservation
                List<SerialNumber> reservedSerialNumbers = serialNumberService.reserveSerialNumbers(
                    productVariantId, quantity, channel, orderId, user);

                // Collect the reserved serial number IDs
                for (SerialNumber serialNumber : reservedSerialNumbers) {
                    reservedSerialNumberIds.add(serialNumber.getId());
                }
            }

            log.info("Reserved {} serial numbers for order {} via channel {}", reservedSerialNumberIds.size(), orderId, channel);
            return reservedSerialNumberIds;

        } catch (Exception e) {
            // If any reservation fails, release all previously reserved items
            releaseReservationSafely(reservedSerialNumberIds);
            throw e;
        }
    }

    /**
     * Reserve product items for an order (legacy method for backward compatibility).
     * Delegates to SerialNumberService for individual unit tracking.
     *
     * @param orderItems List of items to reserve
     * @return List of reserved serial number IDs
     * @throws IllegalArgumentException if insufficient inventory
     */
    @Transactional
    public List<Long> reserveItems(List<HoaDonChiTietDto> orderItems) {
        return reserveItemsWithTracking(orderItems, "SYSTEM", "TEMP-" + System.currentTimeMillis(), "system");
    }

    /**
     * Confirm sale of reserved items (called after successful payment).
     * Delegates to SerialNumberService for individual unit tracking.
     *
     * @param reservedSerialNumberIds List of serial number IDs that were reserved
     * @param orderId Order ID for tracking
     * @param user User performing the confirmation
     */
    @Transactional
    public void confirmSale(List<Long> reservedSerialNumberIds, String orderId, String user) {
        serialNumberService.confirmSale(reservedSerialNumberIds, orderId, user);
        log.info("Confirmed sale of {} serial numbers", reservedSerialNumberIds.size());
    }

    /**
     * Release reservation of items (called when order is cancelled or fails).
     * Delegates to SerialNumberService for individual unit tracking.
     *
     * @param reservedSerialNumberIds List of serial number IDs to release
     * @param user User performing the release
     * @param reason Reason for release
     */
    @Transactional
    public void releaseReservation(List<Long> reservedSerialNumberIds, String user, String reason) {
        serialNumberService.releaseReservations(reservedSerialNumberIds, user, reason);
        log.info("Released reservation for {} serial numbers", reservedSerialNumberIds.size());
    }

    /**
     * Safely release reservation of items (called when order creation fails).
     * Delegates to SerialNumberService for individual unit tracking.
     *
     * @param reservedSerialNumberIds List of serial number IDs to release
     */
    @Transactional
    public void releaseReservationSafely(List<Long> reservedSerialNumberIds) {
        try {
            releaseReservation(reservedSerialNumberIds, "system", "Order creation failed");
        } catch (Exception e) {
            log.warn("Failed to safely release reservations: {}", e.getMessage());
        }
    }

    /**
     * Check available quantity for a product variant.
     * Uses SerialNumberRepository to count available units.
     *
     * @param sanPhamChiTietId Product variant ID
     * @return Number of available individual units
     */
    @Transactional(readOnly = true)
    public int getAvailableQuantity(Long sanPhamChiTietId) {
        return serialNumberService.getAvailableQuantityByVariant(sanPhamChiTietId);
    }

    /**
     * Check if sufficient quantity is available for an order.
     *
     * @param orderItems List of items to check
     * @return true if all items are available in sufficient quantity
     */
    @Transactional(readOnly = true)
    public boolean isInventoryAvailable(List<HoaDonChiTietDto> orderItems) {
        // Handle null input gracefully
        if (orderItems == null || orderItems.isEmpty()) {
            log.warn("Order items list is null or empty, cannot validate inventory");
            return false;
        }

        for (HoaDonChiTietDto orderItem : orderItems) {
            if (orderItem == null || orderItem.getSanPhamChiTietId() == null) {
                log.warn("Order item or product variant ID is null, skipping validation");
                continue;
            }

            int available = getAvailableQuantity(orderItem.getSanPhamChiTietId());
            if (available < orderItem.getSoLuong()) {
                log.warn("Insufficient inventory for product {}: requested {}, available {}",
                    orderItem.getSanPhamChiTietId(), orderItem.getSoLuong(), available);
                return false;
            }
        }
        return true;
    }

    /**
     * Update the order ID for reserved items.
     * For now, this is a placeholder since SerialNumberService doesn't have this method yet.
     *
     * @param reservedSerialNumberIds List of reserved serial number IDs
     * @param oldOrderId Old order ID to replace
     * @param newOrderId New order ID to set
     */
    @Transactional
    public void updateReservationOrderId(List<Long> reservedSerialNumberIds, String oldOrderId, String newOrderId) {
        // TODO: Implement this method in SerialNumberService
        log.info("Updated order ID for {} reserved serial numbers from {} to {} (placeholder)", reservedSerialNumberIds.size(), oldOrderId, newOrderId);
    }

    /**
     * Scheduled task to clean up expired reservations.
     * Delegates to SerialNumberService for individual unit tracking.
     */
    @Scheduled(fixedRate = 300000) // 5 minutes
    @Transactional
    public void cleanupExpiredReservations() {
        try {
            serialNumberService.cleanupExpiredReservations();
        } catch (Exception e) {
            log.error("Error during expired reservation cleanup: {}", e.getMessage(), e);
        }
    }

    // === BACKWARD COMPATIBILITY METHODS ===
    // These methods provide backward compatibility for existing code

    /**
     * Confirm sale of reserved items (backward compatibility method)
     * @param reservedItemIds List of reserved item IDs
     */
    @Transactional
    public void confirmSale(List<Long> reservedItemIds) {
        confirmSale(reservedItemIds, "LEGACY-ORDER", "system");
    }

    /**
     * Release reservation of items (backward compatibility method)
     * @param reservedItemIds List of reserved item IDs
     */
    @Transactional
    public void releaseReservation(List<Long> reservedItemIds) {
        releaseReservation(reservedItemIds, "system", "Legacy release");
    }
}
