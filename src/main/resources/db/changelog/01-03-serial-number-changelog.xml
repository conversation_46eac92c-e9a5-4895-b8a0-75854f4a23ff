<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Create TrangThaiSerialNumber enum type -->
    <changeSet id="create-trang-thai-serial-number-enum" author="admin">
        <sql>
            CREATE TYPE trang_thai_serial_number AS ENUM (
                'AVAILABLE',
                'RESERVED', 
                'SOLD',
                'RETURNED',
                'DAMAGED',
                'UNAVAILABLE',
                'IN_TRANSIT',
                'QUALITY_CONTROL',
                'DISPLAY_UNIT',
                'DISPOSED'
            );
        </sql>
        <rollback>
            DROP TYPE IF EXISTS trang_thai_serial_number;
        </rollback>
    </changeSet>

    <!-- Create serial_number table -->
    <changeSet id="create-serial-number-table" author="admin">
        <createTable tableName="serial_number">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            
            <!-- Serial number value (unique) -->
            <column name="serial_number_value" type="VARCHAR(100)">
                <constraints nullable="false" unique="true"/>
            </column>
            
            <!-- Reference to product variant -->
            <column name="san_pham_chi_tiet_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            
            <!-- Status -->
            <column name="trang_thai" type="trang_thai_serial_number" defaultValue="AVAILABLE">
                <constraints nullable="false"/>
            </column>
            
            <!-- Reservation information -->
            <column name="thoi_gian_dat_truoc" type="TIMESTAMP"/>
            <column name="kenh_dat_truoc" type="VARCHAR(20)"/>
            <column name="don_hang_dat_truoc" type="VARCHAR(50)"/>
            
            <!-- Manufacturing and supplier information -->
            <column name="batch_number" type="VARCHAR(50)"/>
            <column name="ngay_san_xuat" type="TIMESTAMP"/>
            <column name="ngay_het_bao_hanh" type="TIMESTAMP"/>
            <column name="nha_cung_cap" type="VARCHAR(100)"/>
            <column name="import_batch_id" type="VARCHAR(50)"/>
            <column name="ghi_chu" type="VARCHAR(500)"/>
            
            <!-- Audit fields -->
            <column name="ngay_tao" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="TIMESTAMP" defaultValueComputed="CURRENT_TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="deleted" type="BOOLEAN" defaultValueBoolean="false">
                <constraints nullable="false"/>
            </column>
        </createTable>
        
        <!-- Add foreign key constraint -->
        <addForeignKeyConstraint 
            baseTableName="serial_number" 
            baseColumnNames="san_pham_chi_tiet_id"
            constraintName="fk_serial_number_san_pham_chi_tiet"
            referencedTableName="san_pham_chi_tiet"
            referencedColumnNames="id"
            onDelete="RESTRICT"/>
    </changeSet>

    <!-- Create indexes for serial_number table -->
    <changeSet id="create-serial-number-indexes" author="admin">
        <createIndex tableName="serial_number" indexName="idx_serial_number_value">
            <column name="serial_number_value"/>
        </createIndex>
        
        <createIndex tableName="serial_number" indexName="idx_serial_number_status">
            <column name="trang_thai"/>
        </createIndex>
        
        <createIndex tableName="serial_number" indexName="idx_serial_number_variant">
            <column name="san_pham_chi_tiet_id"/>
        </createIndex>
        
        <createIndex tableName="serial_number" indexName="idx_serial_number_reservation">
            <column name="thoi_gian_dat_truoc"/>
        </createIndex>
        
        <createIndex tableName="serial_number" indexName="idx_serial_number_channel">
            <column name="kenh_dat_truoc"/>
        </createIndex>
        
        <createIndex tableName="serial_number" indexName="idx_serial_number_batch">
            <column name="batch_number"/>
        </createIndex>
        
        <createIndex tableName="serial_number" indexName="idx_serial_number_supplier">
            <column name="nha_cung_cap"/>
        </createIndex>
        
        <createIndex tableName="serial_number" indexName="idx_serial_number_warranty">
            <column name="ngay_het_bao_hanh"/>
        </createIndex>
        
        <createIndex tableName="serial_number" indexName="idx_serial_number_import_batch">
            <column name="import_batch_id"/>
        </createIndex>
        
        <!-- Composite indexes for common queries -->
        <createIndex tableName="serial_number" indexName="idx_serial_number_variant_status">
            <column name="san_pham_chi_tiet_id"/>
            <column name="trang_thai"/>
        </createIndex>
        
        <createIndex tableName="serial_number" indexName="idx_serial_number_status_created">
            <column name="trang_thai"/>
            <column name="ngay_tao"/>
        </createIndex>
    </changeSet>

    <!-- Create serial_number_audit_history table -->
    <changeSet id="create-serial-number-audit-history-table" author="admin">
        <createTable tableName="serial_number_audit_history">
            <column name="id" type="BIGINT" autoIncrement="true">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            
            <!-- Reference to serial number -->
            <column name="serial_number_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            
            <!-- Action information -->
            <column name="hanh_dong" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="thoi_gian_thay_doi" type="TIMESTAMP">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_thuc_hien" type="VARCHAR(100)"/>
            <column name="ly_do_thay_doi" type="VARCHAR(500)"/>
            
            <!-- Change details -->
            <column name="gia_tri_cu" type="JSONB"/>
            <column name="gia_tri_moi" type="JSONB"/>
            
            <!-- Client information -->
            <column name="ip_address" type="VARCHAR(45)"/>
            <column name="user_agent" type="VARCHAR(500)"/>
            
            <!-- Operation context -->
            <column name="batch_operation_id" type="VARCHAR(50)"/>
            <column name="order_id" type="VARCHAR(50)"/>
            <column name="channel" type="VARCHAR(20)"/>
            <column name="metadata" type="JSONB"/>
        </createTable>
    </changeSet>

    <!-- Create indexes for serial_number_audit_history table -->
    <changeSet id="create-serial-number-audit-history-indexes" author="admin">
        <createIndex tableName="serial_number_audit_history" indexName="idx_audit_serial_number_id">
            <column name="serial_number_id"/>
        </createIndex>
        
        <createIndex tableName="serial_number_audit_history" indexName="idx_serial_number_audit_timestamp">
            <column name="thoi_gian_thay_doi"/>
        </createIndex>
        
        <createIndex tableName="serial_number_audit_history" indexName="idx_serial_number_audit_action">
            <column name="hanh_dong"/>
        </createIndex>
        
        <createIndex tableName="serial_number_audit_history" indexName="idx_serial_number_audit_user">
            <column name="nguoi_thuc_hien"/>
        </createIndex>
        
        <createIndex tableName="serial_number_audit_history" indexName="idx_serial_number_audit_batch">
            <column name="batch_operation_id"/>
        </createIndex>
        
        <createIndex tableName="serial_number_audit_history" indexName="idx_serial_number_audit_order">
            <column name="order_id"/>
        </createIndex>
        
        <createIndex tableName="serial_number_audit_history" indexName="idx_serial_number_audit_channel">
            <column name="channel"/>
        </createIndex>
        
        <!-- Composite indexes for common queries -->
        <createIndex tableName="serial_number_audit_history" indexName="idx_audit_serial_timestamp">
            <column name="serial_number_id"/>
            <column name="thoi_gian_thay_doi"/>
        </createIndex>
        
        <createIndex tableName="serial_number_audit_history" indexName="idx_audit_action_timestamp">
            <column name="hanh_dong"/>
            <column name="thoi_gian_thay_doi"/>
        </createIndex>
    </changeSet>

    <!-- Create sequences -->
    <changeSet id="create-serial-number-sequences" author="admin">
        <createSequence sequenceName="serial_number_id_seq" startValue="1" incrementBy="1"/>
        <createSequence sequenceName="serial_number_audit_history_id_seq" startValue="1" incrementBy="1"/>
    </changeSet>

    <!-- Add comments to tables and columns -->
    <changeSet id="add-serial-number-table-comments" author="admin">
        <sql>
            COMMENT ON TABLE serial_number IS 'Individual serial numbers for laptop inventory tracking';
            COMMENT ON COLUMN serial_number.serial_number_value IS 'Unique serial number value for each laptop unit';
            COMMENT ON COLUMN serial_number.san_pham_chi_tiet_id IS 'Reference to product variant (SanPhamChiTiet)';
            COMMENT ON COLUMN serial_number.trang_thai IS 'Current status of the serial number';
            COMMENT ON COLUMN serial_number.thoi_gian_dat_truoc IS 'Timestamp when reserved for timeout management';
            COMMENT ON COLUMN serial_number.kenh_dat_truoc IS 'Channel that reserved this unit (POS, ONLINE, etc.)';
            COMMENT ON COLUMN serial_number.don_hang_dat_truoc IS 'Order ID that reserved this unit';
            COMMENT ON COLUMN serial_number.batch_number IS 'Manufacturing batch number';
            COMMENT ON COLUMN serial_number.ngay_san_xuat IS 'Manufacturing date';
            COMMENT ON COLUMN serial_number.ngay_het_bao_hanh IS 'Warranty expiration date';
            COMMENT ON COLUMN serial_number.nha_cung_cap IS 'Supplier/vendor information';
            COMMENT ON COLUMN serial_number.import_batch_id IS 'Import batch reference for bulk operations';
            COMMENT ON COLUMN serial_number.ghi_chu IS 'Additional notes or comments';
            
            COMMENT ON TABLE serial_number_audit_history IS 'Complete audit trail for serial number changes';
            COMMENT ON COLUMN serial_number_audit_history.serial_number_id IS 'ID of the serial number that was changed';
            COMMENT ON COLUMN serial_number_audit_history.hanh_dong IS 'Type of action performed';
            COMMENT ON COLUMN serial_number_audit_history.thoi_gian_thay_doi IS 'Timestamp when the change occurred';
            COMMENT ON COLUMN serial_number_audit_history.nguoi_thuc_hien IS 'User who performed the action';
            COMMENT ON COLUMN serial_number_audit_history.ly_do_thay_doi IS 'Reason for the change';
            COMMENT ON COLUMN serial_number_audit_history.gia_tri_cu IS 'Previous values in JSON format';
            COMMENT ON COLUMN serial_number_audit_history.gia_tri_moi IS 'New values in JSON format';
            COMMENT ON COLUMN serial_number_audit_history.batch_operation_id IS 'Batch operation ID for bulk operations';
            COMMENT ON COLUMN serial_number_audit_history.order_id IS 'Order ID if change is related to an order';
            COMMENT ON COLUMN serial_number_audit_history.channel IS 'Channel where the change occurred';
            COMMENT ON COLUMN serial_number_audit_history.metadata IS 'Additional metadata in JSON format';
        </sql>
    </changeSet>

</databaseChangeLog>
