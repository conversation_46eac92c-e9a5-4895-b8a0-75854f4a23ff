<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.29.xsd"
        objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
    <changeSet id="1748665161152-1" author="obscurites">
        <createSequence incrementBy="1" sequenceName="am_thanh_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-2" author="obscurites">
        <createSequence incrementBy="1" sequenceName="ban_phim_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-3" author="obscurites">
        <createSequence incrementBy="1" sequenceName="bao_mat_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-4" author="obscurites">
        <createSequence incrementBy="1" sequenceName="cong_giao_tiep_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-5" author="obscurites">
        <createSequence incrementBy="1" sequenceName="cpu_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-6" author="obscurites">
        <createSequence incrementBy="1" sequenceName="danh_gia_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-7" author="obscurites">
        <createSequence incrementBy="1" sequenceName="danh_muc_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-8" author="obscurites">
        <createSequence incrementBy="1" sequenceName="danh_sach_yeu_thich_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-9" author="obscurites">
        <createSequence incrementBy="1" sequenceName="dia_chi_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-10" author="obscurites">
        <createSequence incrementBy="1" sequenceName="dot_giam_gia_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-11" author="obscurites">
        <createSequence incrementBy="1" sequenceName="gio_hang_chi_tiet_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-12" author="obscurites">
        <createSequence incrementBy="1" sequenceName="gio_hang_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-13" author="obscurites">
        <createSequence incrementBy="1" sequenceName="gpu_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-14" author="obscurites">
        <createSequence incrementBy="1" sequenceName="he_dieu_hanh_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-15" author="obscurites">
        <createSequence incrementBy="1" sequenceName="hoa_don_chi_tiet_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-16" author="obscurites">
        <createSequence incrementBy="1" sequenceName="hoa_don_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-17" author="obscurites">
        <createSequence incrementBy="1" sequenceName="ket_noi_mang_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-18" author="obscurites">
        <createSequence incrementBy="1" sequenceName="man_hinh_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-19" author="obscurites">
        <createSequence incrementBy="1" sequenceName="nguoi_dung_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-20" author="obscurites">
        <createSequence incrementBy="1" sequenceName="o_cung_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-21" author="obscurites">
        <createSequence incrementBy="1" sequenceName="phieu_giam_gia_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-22" author="obscurites">
        <createSequence incrementBy="1" sequenceName="pin_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-23" author="obscurites">
        <createSequence incrementBy="1" sequenceName="ram_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-24" author="obscurites">
        <createSequence incrementBy="1" sequenceName="san_pham_chi_tiet_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-25" author="obscurites">
        <createSequence incrementBy="1" sequenceName="san_pham_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-26" author="obscurites">
        <createSequence incrementBy="1" sequenceName="thanh_toan_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-27" author="obscurites">
        <createSequence incrementBy="1" sequenceName="thiet_ke_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-28" author="obscurites">
        <createSequence incrementBy="1" sequenceName="thuong_hieu_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-29" author="obscurites">
        <createSequence incrementBy="1" sequenceName="webcam_id_seq" startValue="1"/>
    </changeSet>
    <changeSet id="1748665161152-30" author="obscurites">
        <createTable tableName="am_thanh">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_am_thanh"/>
            </column>
            <column name="mo_ta_am_thanh" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-31" author="obscurites">
        <createTable tableName="ban_phim">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_ban_phim"/>
            </column>
            <column name="mo_ta_ban_phim" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-32" author="obscurites">
        <createTable tableName="bao_mat">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_bao_mat"/>
            </column>
            <column name="mo_ta_bao_mat" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-33" author="obscurites">
        <createTable tableName="chuyen_doi_trang_thai_hoa_don">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_chuyen_doi_trang_thai_hoa_don"/>
            </column>
            <column name="trang_thai_tu" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="trang_thai_den" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="cho_phep" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="vai_tro_yeu_cau" type="VARCHAR(255)"/>
            <column name="quy_tac_kinh_doanh" type="VARCHAR(500)"/>
            <column name="yeu_cau_ly_do" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="chi_he_thong" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_tao" type="DATETIME"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-34" author="obscurites">
        <createTable tableName="cong_giao_tiep">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_cong_giao_tiep"/>
            </column>
            <column name="mo_ta_cong" type="VARCHAR(512)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-35" author="obscurites">
        <createTable tableName="cpu">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_cpu"/>
            </column>
            <column name="mo_ta_cpu" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-36" author="obscurites">
        <createTable tableName="danh_gia">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_danh_gia"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="nguoi_dung_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="san_pham_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hoa_don_chi_tiet_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="diem_danh_gia" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="noi_dung" type="VARCHAR(1000)"/>
            <column name="tieu_de" type="VARCHAR(200)"/>
            <column name="hinh_anh" type="jsonb"/>
            <column name="trang_thai" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-37" author="obscurites">
        <createTable tableName="danh_muc">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_danh_muc"/>
            </column>
            <column name="ma_danh_muc" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="ten_danh_muc" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-38" author="obscurites">
        <createTable tableName="danh_sach_yeu_thich">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_danh_sach_yeu_thich"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="nguoi_dung_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="san_pham_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="gia_khi_them" type="DECIMAL(15, 2)"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-39" author="obscurites">
        <createTable tableName="dia_chi">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_dia_chi"/>
            </column>
            <column name="nguoi_dung_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="duong" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="phuong_xa" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="quan_huyen" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="tinh_thanh" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="Việt Nam" name="quoc_gia" type="varchar(100)"/>
            <column name="loai_dia_chi" type="VARCHAR(50)"/>
            <column name="la_mac_dinh" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="ngay_tao" type="timestamp with time zone"/>
            <column defaultValueComputed="CURRENT_TIMESTAMP" name="ngay_cap_nhat" type="timestamp with time zone"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-40" author="obscurites">
        <createTable tableName="dot_giam_gia">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_dot_giam_gia"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="ma_dot_giam_gia" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="ten_dot_giam_gia" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="phan_tram_giam" type="DECIMAL(5, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_bat_dau" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_ket_thuc" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column defaultValueBoolean="false" name="da_an" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="CHUA_DIEN_RA" name="trang_thai" type="VARCHAR(255)"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-41" author="obscurites">
        <createTable tableName="dot_giam_gia_audit_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_dot_giam_gia_audit_history"/>
            </column>
            <column name="dot_giam_gia_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hanh_dong" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="thoi_gian_thay_doi" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_thuc_hien" type="VARCHAR(100)"/>
            <column name="ly_do_thay_doi" type="VARCHAR(500)"/>
            <column name="gia_tri_cu" type="jsonb"/>
            <column name="gia_tri_moi" type="jsonb"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-42" author="obscurites">
        <createTable tableName="gio_hang">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_gio_hang"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="nguoi_dung_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-43" author="obscurites">
        <createTable tableName="gio_hang_chi_tiet">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_gio_hang_chi_tiet"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="gio_hang_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="san_pham_chi_tiet_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="so_luong" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="gia_tai_thoi_diem_them" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-44" author="obscurites">
        <createTable tableName="gpu">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_gpu"/>
            </column>
            <column name="mo_ta_gpu" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-45" author="obscurites">
        <createTable tableName="he_dieu_hanh">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_he_dieu_hanh"/>
            </column>
            <column name="mo_ta_he_dieu_hanh" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-46" author="obscurites">
        <createTable tableName="hoa_don">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_hoa_don"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="ma_hoa_don" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="khach_hang_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="nhan_vien_id" type="BIGINT"/>
            <column name="dia_chi_giao_hang_id" type="BIGINT"/>
            <column name="nguoi_nhan_ten" type="VARCHAR(255)"/>
            <column name="nguoi_nhan_sdt" type="VARCHAR(20)"/>
            <column name="tong_tien_hang" type="DECIMAL(15, 2)"/>
            <column name="gia_tri_giam_gia_voucher" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="phi_van_chuyen" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="tong_thanh_toan" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="trang_thai_don_hang" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="trang_thai_thanh_toan" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="loai_hoa_don" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="ma_van_don" type="VARCHAR(100)"/>
            <column name="ngay_du_kien_giao_hang" type="DATETIME"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-47" author="obscurites">
        <createTable tableName="hoa_don_audit_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_hoa_don_audit_history"/>
            </column>
            <column name="hoa_don_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hanh_dong" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="thoi_gian_thay_doi" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_thuc_hien" type="VARCHAR(100)"/>
            <column name="ly_do_thay_doi" type="VARCHAR(500)"/>
            <column name="gia_tri_cu" type="jsonb"/>
            <column name="gia_tri_moi" type="jsonb"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-48" author="obscurites">
        <createTable tableName="hoa_don_chi_tiet">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_hoa_don_chi_tiet"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="hoa_don_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="san_pham_chi_tiet_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="so_luong" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="gia_goc" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="gia_ban" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="thanh_tien" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="ten_san_pham_snapshot" type="VARCHAR(255)"/>
            <column name="sku_snapshot" type="VARCHAR(100)"/>
            <column name="hinh_anh_snapshot" type="VARCHAR(512)"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-49" author="obscurites">
        <createTable tableName="hoa_don_phieu_giam_gia">
            <column name="gia_tri_da_giam" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="hoa_don_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_hoa_don_phieu_giam_gia"/>
            </column>
            <column name="phieu_giam_gia_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_hoa_don_phieu_giam_gia"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-50" author="obscurites">
        <createTable tableName="hoa_don_thanh_toan">
            <column defaultValueNumeric="0" name="so_tien_ap_dung" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="hoa_don_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_hoa_don_thanh_toan"/>
            </column>
            <column name="thanh_toan_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_hoa_don_thanh_toan"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-51" author="obscurites">
        <createTable tableName="ket_noi_mang">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_ket_noi_mang"/>
            </column>
            <column name="mo_ta_ket_noi" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-52" author="obscurites">
        <createTable tableName="man_hinh">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_man_hinh"/>
            </column>
            <column name="mo_ta_man_hinh" type="VARCHAR(300)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-53" author="obscurites">
        <createTable tableName="nguoi_dung">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_nguoi_dung"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="ma_nguoi_dung" type="VARCHAR(50)"/>
            <column name="avatar" type="VARCHAR(512)"/>
            <column name="ho_ten" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="gioi_tinh" type="VARCHAR(255)"/>
            <column name="ngay_sinh" type="DATE"/>
            <column name="cccd" type="VARCHAR(12)"/>
            <column name="email" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="so_dien_thoai" type="VARCHAR(20)"/>
            <column name="mat_khau" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="vai_tro" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="trang_thai" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-54" author="obscurites">
        <createTable tableName="nguoi_dung_audit_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_nguoi_dung_audit_history"/>
            </column>
            <column name="nguoi_dung_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hanh_dong" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="thoi_gian_thay_doi" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_thuc_hien" type="VARCHAR(100)"/>
            <column name="ly_do_thay_doi" type="VARCHAR(500)"/>
            <column name="gia_tri_cu" type="jsonb"/>
            <column name="gia_tri_moi" type="jsonb"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-55" author="obscurites">
        <createTable tableName="o_cung">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_o_cung"/>
            </column>
            <column name="mo_ta_o_cung" type="VARCHAR(150)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-56" author="obscurites">
        <createTable tableName="phieu_giam_gia">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_phieu_giam_gia"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="ma_phieu_giam_gia" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="loai_giam_gia" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="trang_thai" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="gia_tri_giam" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="gia_tri_don_hang_toi_thieu" type="DECIMAL(15, 2)"/>
            <column name="ngay_bat_dau" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_ket_thuc" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="mo_ta" type="text(2147483647)"/>
            <column name="so_luong_ban_dau" type="INT">
                <constraints nullable="false"/>
            </column>
            <column name="so_luong_da_dung" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-57" author="obscurites">
        <createTable tableName="phieu_giam_gia_audit_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_phieu_giam_gia_audit_history"/>
            </column>
            <column name="phieu_giam_gia_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hanh_dong" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="thoi_gian_thay_doi" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_thuc_hien" type="VARCHAR(100)"/>
            <column name="ly_do_thay_doi" type="VARCHAR(500)"/>
            <column name="gia_tri_cu" type="jsonb"/>
            <column name="gia_tri_moi" type="jsonb"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-58" author="obscurites">
        <createTable tableName="phieu_giam_gia_nguoi_dung">
            <column name="ngay_nhan" type="timestamp"/>
            <column name="da_su_dung" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_su_dung" type="DATETIME"/>
            <column name="ngay_tao" type="DATETIME"/>
            <column name="ngay_cap_nhat" type="DATETIME"/>
            <column name="phieu_giam_gia_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_phieu_giam_gia_nguoi_dung"/>
            </column>
            <column name="nguoi_dung_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_phieu_giam_gia_nguoi_dung"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-59" author="obscurites">
        <createTable tableName="pin">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_pin"/>
            </column>
            <column name="mo_ta_pin" type="VARCHAR(150)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-60" author="obscurites">
        <createTable tableName="ram">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_ram"/>
            </column>
            <column name="mo_ta_ram" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-61" author="obscurites">
        <createTable tableName="san_pham">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_san_pham"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="ma_san_pham" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="ten_san_pham" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="thuong_hieu_id" type="BIGINT"/>
            <column name="mo_ta" type="VARCHAR(5000)"/>
            <column name="hinh_anh" type="jsonb"/>
            <column name="ngay_ra_mat" type="DATE"/>
            <column defaultValueBoolean="true" name="trang_thai" type="BOOLEAN">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-62" author="obscurites">
        <createTable tableName="san_pham_audit_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_san_pham_audit_history"/>
            </column>
            <column name="san_pham_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hanh_dong" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="thoi_gian_thay_doi" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_thuc_hien" type="VARCHAR(100)"/>
            <column name="ly_do_thay_doi" type="VARCHAR(500)"/>
            <column name="gia_tri_cu" type="jsonb"/>
            <column name="gia_tri_moi" type="jsonb"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-63" author="obscurites">
        <createTable tableName="san_pham_chi_tiet">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_san_pham_chi_tiet"/>
            </column>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_tao" type="VARCHAR(100)"/>
            <column name="nguoi_cap_nhat" type="VARCHAR(100)"/>
            <column name="san_pham_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="serial_number" type="VARCHAR(100)">
                <constraints nullable="false"/>
            </column>
            <column name="mau_sac" type="VARCHAR(50)"/>
            <column name="gia_ban" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="gia_khuyen_mai" type="DECIMAL(15, 2)"/>
            <column name="hinh_anh" type="jsonb"/>
            <column defaultValue="AVAILABLE" name="trang_thai" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="cpu_id" type="BIGINT"/>
            <column name="ram_id" type="BIGINT"/>
            <column name="o_cung_id" type="BIGINT"/>
            <column name="gpu_id" type="BIGINT"/>
            <column name="man_hinh_id" type="BIGINT"/>
            <column name="cong_giao_tiep_id" type="BIGINT"/>
            <column name="ban_phim_id" type="BIGINT"/>
            <column name="ket_noi_mang_id" type="BIGINT"/>
            <column name="am_thanh_id" type="BIGINT"/>
            <column name="webcam_id" type="BIGINT"/>
            <column name="bao_mat_id" type="BIGINT"/>
            <column name="he_dieu_hanh_id" type="BIGINT"/>
            <column name="pin_id" type="BIGINT"/>
            <column name="thiet_ke_id" type="BIGINT"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-64" author="obscurites">
        <createTable tableName="san_pham_chi_tiet_audit_history">
            <column autoIncrement="true" name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_san_pham_chi_tiet_audit_history"/>
            </column>
            <column name="san_pham_chi_tiet_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="hanh_dong" type="VARCHAR(50)">
                <constraints nullable="false"/>
            </column>
            <column name="thoi_gian_thay_doi" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="nguoi_thuc_hien" type="VARCHAR(100)"/>
            <column name="ly_do_thay_doi" type="VARCHAR(500)"/>
            <column name="gia_tri_cu" type="jsonb"/>
            <column name="gia_tri_moi" type="jsonb"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-65" author="obscurites">
        <createTable tableName="san_pham_chi_tiet_dot_giam_gia">
            <column name="dot_giam_gia_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_san_pham_chi_tiet_dot_giam_gia"/>
            </column>
            <column name="san_pham_chi_tiet_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_san_pham_chi_tiet_dot_giam_gia"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-66" author="obscurites">
        <createTable tableName="san_pham_danh_muc">
            <column name="danh_muc_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_san_pham_danh_muc"/>
            </column>
            <column name="san_pham_id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_san_pham_danh_muc"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-67" author="obscurites">
        <createTable tableName="thanh_toan">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_thanh_toan"/>
            </column>
            <column name="nguoi_dung_id" type="BIGINT">
                <constraints nullable="false"/>
            </column>
            <column name="ma_giao_dich" type="VARCHAR(255)"/>
            <column name="gia_tri" type="DECIMAL(15, 2)">
                <constraints nullable="false"/>
            </column>
            <column name="ghi_chu" type="text(2147483647)"/>
            <column name="thoi_gian_thanh_toan" type="DATETIME"/>
            <column name="ngay_tao" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="ngay_cap_nhat" type="DATETIME">
                <constraints nullable="false"/>
            </column>
            <column name="trang_thai_giao_dich" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
            <column name="phuong_thuc_thanh_toan" type="VARCHAR(255)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-68" author="obscurites">
        <createTable tableName="thiet_ke">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_thiet_ke"/>
            </column>
            <column name="mo_ta_thiet_ke" type="VARCHAR(300)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-69" author="obscurites">
        <createTable tableName="thong_ke_doanh_so_top_hang_ngay">
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_thong_ke_doanh_so_top_hang_ngay"/>
            </column>
            <column name="sales_date" type="DATETIME"/>
            <column name="brand" type="VARCHAR(255)"/>
            <column name="sale" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-70" author="obscurites">
        <createTable tableName="thong_ke_doanh_thu_hang_ngay">
            <column name="id" type="INT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_thong_ke_doanh_thu_hang_ngay"/>
            </column>
            <column name="revenue_date" type="DATETIME"/>
            <column name="brand" type="VARCHAR(255)"/>
            <column name="revenue" type="INT"/>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-71" author="obscurites">
        <createTable tableName="thuong_hieu">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_thuong_hieu"/>
            </column>
            <column name="mo_ta_thuong_hieu" type="VARCHAR(300)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-72" author="obscurites">
        <createTable tableName="webcam">
            <column name="id" type="BIGINT">
                <constraints nullable="false" primaryKey="true" primaryKeyName="pk_webcam"/>
            </column>
            <column name="mo_ta_wc" type="VARCHAR(200)">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
    <changeSet id="1748665161152-73" author="obscurites">
        <addUniqueConstraint columnNames="trang_thai_tu, trang_thai_den" constraintName="uc_42e70b9f9175194276a8a4fcc"
                             tableName="chuyen_doi_trang_thai_hoa_don"/>
    </changeSet>
    <changeSet id="1748665161152-74" author="obscurites">
        <addUniqueConstraint columnNames="nguoi_dung_id" constraintName="uc_gio_hang_nguoi_dung" tableName="gio_hang"/>
    </changeSet>
    <changeSet id="1748665161152-75" author="obscurites">
        <addUniqueConstraint columnNames="ma_hoa_don" constraintName="uc_hoa_don_ma_hoa_don" tableName="hoa_don"/>
    </changeSet>
    <changeSet id="1748665161152-76" author="obscurites">
        <addUniqueConstraint columnNames="cccd" constraintName="uc_nguoi_dung_cccd" tableName="nguoi_dung"/>
    </changeSet>
    <changeSet id="1748665161152-77" author="obscurites">
        <addUniqueConstraint columnNames="email" constraintName="uc_nguoi_dung_email" tableName="nguoi_dung"/>
    </changeSet>
    <changeSet id="1748665161152-78" author="obscurites">
        <addUniqueConstraint columnNames="ma_nguoi_dung" constraintName="uc_nguoi_dung_ma_nguoi_dung"
                             tableName="nguoi_dung"/>
    </changeSet>
    <changeSet id="1748665161152-79" author="obscurites">
        <addUniqueConstraint columnNames="so_dien_thoai" constraintName="uc_nguoi_dung_so_dien_thoai"
                             tableName="nguoi_dung"/>
    </changeSet>
    <changeSet id="1748665161152-80" author="obscurites">
        <addUniqueConstraint columnNames="ma_phieu_giam_gia" constraintName="uc_phieu_giam_gia_ma_phieu_giam_gia"
                             tableName="phieu_giam_gia"/>
    </changeSet>
    <changeSet id="1748665161152-81" author="obscurites">
        <addUniqueConstraint columnNames="serial_number" constraintName="uc_san_pham_chi_tiet_serial_number"
                             tableName="san_pham_chi_tiet"/>
    </changeSet>
    <changeSet id="1748665161152-82" author="obscurites">
        <addUniqueConstraint columnNames="gio_hang_id, san_pham_chi_tiet_id" constraintName="uk_gio_hang_san_pham"
                             tableName="gio_hang_chi_tiet"/>
    </changeSet>
    <changeSet id="1748665161152-83" author="obscurites">
        <addUniqueConstraint columnNames="hoa_don_chi_tiet_id" constraintName="uk_hoa_don_chi_tiet"
                             tableName="danh_gia"/>
    </changeSet>
    <changeSet id="1748665161152-84" author="obscurites">
        <addUniqueConstraint columnNames="nguoi_dung_id, san_pham_id" constraintName="uk_nguoi_dung_san_pham"
                             tableName="danh_sach_yeu_thich"/>
    </changeSet>
    <changeSet id="1748665161152-85" author="obscurites">
        <createIndex indexName="idx_dot_giam_gia_audit_action" tableName="dot_giam_gia_audit_history">
            <column name="hanh_dong"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-86" author="obscurites">
        <createIndex indexName="idx_hoa_don_audit_action" tableName="hoa_don_audit_history">
            <column name="hanh_dong"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-87" author="obscurites">
        <createIndex indexName="idx_nguoi_dung_audit_action" tableName="nguoi_dung_audit_history">
            <column name="hanh_dong"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-88" author="obscurites">
        <createIndex indexName="idx_phieu_giam_gia_audit_action" tableName="phieu_giam_gia_audit_history">
            <column name="hanh_dong"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-89" author="obscurites">
        <createIndex indexName="idx_san_pham_audit_action" tableName="san_pham_audit_history">
            <column name="hanh_dong"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-90" author="obscurites">
        <createIndex indexName="idx_san_pham_chi_tiet_audit_action" tableName="san_pham_chi_tiet_audit_history">
            <column name="hanh_dong"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-91" author="obscurites">
        <createIndex indexName="idx_audit_dot_giam_gia_id" tableName="dot_giam_gia_audit_history">
            <column name="dot_giam_gia_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-92" author="obscurites">
        <createIndex indexName="idx_audit_hoa_don_id" tableName="hoa_don_audit_history">
            <column name="hoa_don_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-93" author="obscurites">
        <createIndex indexName="idx_audit_nguoi_dung_id" tableName="nguoi_dung_audit_history">
            <column name="nguoi_dung_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-94" author="obscurites">
        <createIndex indexName="idx_audit_phieu_id" tableName="phieu_giam_gia_audit_history">
            <column name="phieu_giam_gia_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-95" author="obscurites">
        <createIndex indexName="idx_audit_san_pham_chi_tiet_id" tableName="san_pham_chi_tiet_audit_history">
            <column name="san_pham_chi_tiet_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-96" author="obscurites">
        <createIndex indexName="idx_audit_san_pham_id" tableName="san_pham_audit_history">
            <column name="san_pham_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-97" author="obscurites">
        <createIndex indexName="idx_dot_giam_gia_audit_timestamp" tableName="dot_giam_gia_audit_history">
            <column name="thoi_gian_thay_doi"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-98" author="obscurites">
        <createIndex indexName="idx_hoa_don_audit_timestamp" tableName="hoa_don_audit_history">
            <column name="thoi_gian_thay_doi"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-99" author="obscurites">
        <createIndex indexName="idx_nguoi_dung_audit_timestamp" tableName="nguoi_dung_audit_history">
            <column name="thoi_gian_thay_doi"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-100" author="obscurites">
        <createIndex indexName="idx_phieu_giam_gia_audit_timestamp" tableName="phieu_giam_gia_audit_history">
            <column name="thoi_gian_thay_doi"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-101" author="obscurites">
        <createIndex indexName="idx_san_pham_audit_timestamp" tableName="san_pham_audit_history">
            <column name="thoi_gian_thay_doi"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-102" author="obscurites">
        <createIndex indexName="idx_san_pham_chi_tiet_audit_timestamp" tableName="san_pham_chi_tiet_audit_history">
            <column name="thoi_gian_thay_doi"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-103" author="obscurites">
        <createIndex indexName="idx_chuyen_doi_trang_thai_den" tableName="chuyen_doi_trang_thai_hoa_don">
            <column name="trang_thai_den"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-104" author="obscurites">
        <createIndex indexName="idx_chuyen_doi_trang_thai_tu" tableName="chuyen_doi_trang_thai_hoa_don">
            <column name="trang_thai_tu"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-105" author="obscurites">
        <createIndex indexName="idx_danh_gia_diem" tableName="danh_gia">
            <column name="diem_danh_gia"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-106" author="obscurites">
        <createIndex indexName="idx_danh_gia_ngay_tao" tableName="danh_gia">
            <column name="ngay_tao"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-109" author="obscurites">
        <createIndex indexName="idx_danh_gia_trang_thai" tableName="danh_gia">
            <column name="trang_thai"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-110" author="obscurites">
        <createIndex indexName="idx_danh_sach_yeu_thich_ngay_them" tableName="danh_sach_yeu_thich">
            <column name="ngay_tao"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-113" author="obscurites">
        <createIndex indexName="idx_dot_giam_gia_ma" tableName="dot_giam_gia">
            <column name="ma_dot_giam_gia"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-114" author="obscurites">
        <createIndex indexName="idx_dot_giam_gia_ngay_bat_dau" tableName="dot_giam_gia">
            <column name="ngay_bat_dau"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-115" author="obscurites">
        <createIndex indexName="idx_dot_giam_gia_ngay_ket_thuc" tableName="dot_giam_gia">
            <column name="ngay_ket_thuc"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-116" author="obscurites">
        <createIndex indexName="idx_dot_giam_gia_trang_thai" tableName="dot_giam_gia">
            <column name="trang_thai"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-118" author="obscurites">
        <createIndex indexName="idx_gio_hang_chi_tiet_ngay_them" tableName="gio_hang_chi_tiet">
            <column name="ngay_tao"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-120" author="obscurites">
        <createIndex indexName="idx_gio_hang_ngay_cap_nhat" tableName="gio_hang">
            <column name="ngay_cap_nhat"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-127" author="obscurites">
        <createIndex indexName="idx_hoa_don_ngay_tao" tableName="hoa_don">
            <column name="ngay_tao"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-128" author="obscurites">
        <createIndex indexName="idx_hoa_don_trang_thai" tableName="hoa_don">
            <column name="trang_thai_don_hang"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-131" author="obscurites">
        <createIndex indexName="idx_nguoi_dung_trang_thai" tableName="nguoi_dung">
            <column name="trang_thai"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-132" author="obscurites">
        <createIndex indexName="idx_nguoi_dung_vai_tro" tableName="nguoi_dung">
            <column name="vai_tro"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-133" author="obscurites">
        <createIndex indexName="idx_phieu_giam_gia_nguoi_dung_da_su_dung" tableName="phieu_giam_gia_nguoi_dung">
            <column name="da_su_dung"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-134" author="obscurites">
        <createIndex indexName="idx_phieu_giam_gia_nguoi_dung_ngay_nhan" tableName="phieu_giam_gia_nguoi_dung">
            <column name="ngay_nhan"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-135" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="hoa_don_chi_tiet_id" baseTableName="danh_gia"
                                 constraintName="FK_DANH_GIA_ON_HOA_DON_CHI_TIET" referencedColumnNames="id"
                                 referencedTableName="hoa_don_chi_tiet"/>
    </changeSet>
    <changeSet id="1748665161152-136" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="nguoi_dung_id" baseTableName="danh_gia"
                                 constraintName="FK_DANH_GIA_ON_NGUOI_DUNG" referencedColumnNames="id"
                                 referencedTableName="nguoi_dung"/>

        <createIndex indexName="idx_danh_gia_nguoi_dung" tableName="danh_gia">
            <column name="nguoi_dung_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-137" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="san_pham_id" baseTableName="danh_gia"
                                 constraintName="FK_DANH_GIA_ON_SAN_PHAM" referencedColumnNames="id"
                                 referencedTableName="san_pham"/>

        <createIndex indexName="idx_danh_gia_san_pham" tableName="danh_gia">
            <column name="san_pham_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-138" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="nguoi_dung_id" baseTableName="danh_sach_yeu_thich"
                                 constraintName="FK_DANH_SACH_YEU_THICH_ON_NGUOI_DUNG" referencedColumnNames="id"
                                 referencedTableName="nguoi_dung"/>

        <createIndex indexName="idx_danh_sach_yeu_thich_nguoi_dung" tableName="danh_sach_yeu_thich">
            <column name="nguoi_dung_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-139" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="san_pham_id" baseTableName="danh_sach_yeu_thich"
                                 constraintName="FK_DANH_SACH_YEU_THICH_ON_SAN_PHAM" referencedColumnNames="id"
                                 referencedTableName="san_pham"/>

        <createIndex indexName="idx_danh_sach_yeu_thich_san_pham" tableName="danh_sach_yeu_thich">
            <column name="san_pham_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-140" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="nguoi_dung_id" baseTableName="dia_chi"
                                 constraintName="FK_DIA_CHI_ON_NGUOI_DUNG" referencedColumnNames="id"
                                 referencedTableName="nguoi_dung"/>
    </changeSet>
    <changeSet id="1748665161152-141" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="gio_hang_id" baseTableName="gio_hang_chi_tiet"
                                 constraintName="FK_GIO_HANG_CHI_TIET_ON_GIO_HANG" referencedColumnNames="id"
                                 referencedTableName="gio_hang"/>

        <createIndex indexName="idx_gio_hang_chi_tiet_gio_hang" tableName="gio_hang_chi_tiet">
            <column name="gio_hang_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-142" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="san_pham_chi_tiet_id" baseTableName="gio_hang_chi_tiet"
                                 constraintName="FK_GIO_HANG_CHI_TIET_ON_SAN_PHAM_CHI_TIET" referencedColumnNames="id"
                                 referencedTableName="san_pham_chi_tiet"/>

        <createIndex indexName="idx_gio_hang_chi_tiet_san_pham" tableName="gio_hang_chi_tiet">
            <column name="san_pham_chi_tiet_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-143" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="nguoi_dung_id" baseTableName="gio_hang"
                                 constraintName="FK_GIO_HANG_ON_NGUOI_DUNG" referencedColumnNames="id"
                                 referencedTableName="nguoi_dung"/>
    </changeSet>
    <changeSet id="1748665161152-144" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="hoa_don_id" baseTableName="hoa_don_chi_tiet"
                                 constraintName="FK_HOA_DON_CHI_TIET_ON_HOA_DON" onDelete="CASCADE"
                                 referencedColumnNames="id" referencedTableName="hoa_don"/>

        <createIndex indexName="idx_hoa_don_chi_tiet_hoa_don" tableName="hoa_don_chi_tiet">
            <column name="hoa_don_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-145" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="san_pham_chi_tiet_id" baseTableName="hoa_don_chi_tiet"
                                 constraintName="FK_HOA_DON_CHI_TIET_ON_SAN_PHAM_CHI_TIET" onDelete="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="san_pham_chi_tiet"/>

        <createIndex indexName="idx_hoa_don_chi_tiet_san_pham" tableName="hoa_don_chi_tiet">
            <column name="san_pham_chi_tiet_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-146" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="dia_chi_giao_hang_id" baseTableName="hoa_don"
                                 constraintName="FK_HOA_DON_ON_DIA_CHI_GIAO_HANG" referencedColumnNames="id"
                                 referencedTableName="dia_chi"/>

        <createIndex indexName="idx_hoa_don_dia_chi" tableName="hoa_don">
            <column name="dia_chi_giao_hang_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-147" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="khach_hang_id" baseTableName="hoa_don"
                                 constraintName="FK_HOA_DON_ON_KHACH_HANG" referencedColumnNames="id"
                                 referencedTableName="nguoi_dung"/>

        <createIndex indexName="idx_hoa_don_khach_hang" tableName="hoa_don">
            <column name="khach_hang_id"/>
        </createIndex>
    </changeSet>
    <changeSet id="1748665161152-148" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="nhan_vien_id" baseTableName="hoa_don"
                                 constraintName="FK_HOA_DON_ON_NHAN_VIEN" referencedColumnNames="id"
                                 referencedTableName="nguoi_dung"/>
    </changeSet>
    <changeSet id="1748665161152-149" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="hoa_don_id" baseTableName="hoa_don_phieu_giam_gia"
                                 constraintName="FK_HOA_DON_PHIEU_GIAM_GIA_ON_HOA_DON" onDelete="CASCADE"
                                 referencedColumnNames="id" referencedTableName="hoa_don"/>
    </changeSet>
    <changeSet id="1748665161152-150" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="phieu_giam_gia_id" baseTableName="hoa_don_phieu_giam_gia"
                                 constraintName="FK_HOA_DON_PHIEU_GIAM_GIA_ON_PHIEU_GIAM_GIA" onDelete="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="phieu_giam_gia"/>
    </changeSet>
    <changeSet id="1748665161152-151" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="hoa_don_id" baseTableName="hoa_don_thanh_toan"
                                 constraintName="FK_HOA_DON_THANH_TOAN_ON_HOA_DON" onDelete="CASCADE"
                                 referencedColumnNames="id" referencedTableName="hoa_don"/>
    </changeSet>
    <changeSet id="1748665161152-152" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="thanh_toan_id" baseTableName="hoa_don_thanh_toan"
                                 constraintName="FK_HOA_DON_THANH_TOAN_ON_THANH_TOAN" onDelete="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="thanh_toan"/>
    </changeSet>
    <changeSet id="1748665161152-153" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="nguoi_dung_id" baseTableName="phieu_giam_gia_nguoi_dung"
                                 constraintName="FK_PHIEU_GIAM_GIA_NGUOI_DUNG_ON_NGUOI_DUNG" onDelete="CASCADE"
                                 referencedColumnNames="id" referencedTableName="nguoi_dung"/>
    </changeSet>
    <changeSet id="1748665161152-154" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="phieu_giam_gia_id" baseTableName="phieu_giam_gia_nguoi_dung"
                                 constraintName="FK_PHIEU_GIAM_GIA_NGUOI_DUNG_ON_PHIEU_GIAM_GIA" onDelete="CASCADE"
                                 referencedColumnNames="id" referencedTableName="phieu_giam_gia"/>
    </changeSet>
    <changeSet id="1748665161152-155" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="am_thanh_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_AM_THANH" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="am_thanh"/>
    </changeSet>
    <changeSet id="1748665161152-156" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="ban_phim_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_BAN_PHIM" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="ban_phim"/>
    </changeSet>
    <changeSet id="1748665161152-157" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="bao_mat_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_BAO_MAT" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="bao_mat"/>
    </changeSet>
    <changeSet id="1748665161152-158" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="cong_giao_tiep_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_CONG_GIAO_TIEP" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="cong_giao_tiep"/>
    </changeSet>
    <changeSet id="1748665161152-159" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="cpu_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_CPU" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="cpu"/>
    </changeSet>
    <changeSet id="1748665161152-160" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="gpu_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_GPU" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="gpu"/>
    </changeSet>
    <changeSet id="1748665161152-161" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="he_dieu_hanh_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_HE_DIEU_HANH" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="he_dieu_hanh"/>
    </changeSet>
    <changeSet id="1748665161152-162" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="ket_noi_mang_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_KET_NOI_MANG" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="ket_noi_mang"/>
    </changeSet>
    <changeSet id="1748665161152-163" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="man_hinh_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_MAN_HINH" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="man_hinh"/>
    </changeSet>
    <changeSet id="1748665161152-164" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="o_cung_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_O_CUNG" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="o_cung"/>
    </changeSet>
    <changeSet id="1748665161152-165" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="pin_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_PIN" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="pin"/>
    </changeSet>
    <changeSet id="1748665161152-166" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="ram_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_RAM" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="ram"/>
    </changeSet>
    <changeSet id="1748665161152-167" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="san_pham_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_SAN_PHAM" onDelete="CASCADE"
                                 referencedColumnNames="id" referencedTableName="san_pham"/>
    </changeSet>
    <changeSet id="1748665161152-168" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="thiet_ke_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_THIET_KE" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="thiet_ke"/>
    </changeSet>
    <changeSet id="1748665161152-169" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="webcam_id" baseTableName="san_pham_chi_tiet"
                                 constraintName="FK_SAN_PHAM_CHI_TIET_ON_WEBCAM" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="webcam"/>
    </changeSet>
    <changeSet id="1748665161152-170" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="thuong_hieu_id" baseTableName="san_pham"
                                 constraintName="FK_SAN_PHAM_ON_THUONG_HIEU" onDelete="SET NULL"
                                 referencedColumnNames="id" referencedTableName="thuong_hieu"/>
    </changeSet>
    <changeSet id="1748665161152-171" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="nguoi_dung_id" baseTableName="thanh_toan"
                                 constraintName="FK_THANH_TOAN_ON_NGUOI_DUNG" onDelete="RESTRICT"
                                 referencedColumnNames="id" referencedTableName="nguoi_dung"/>
    </changeSet>
    <changeSet id="1748665161152-172" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="dot_giam_gia_id" baseTableName="san_pham_chi_tiet_dot_giam_gia"
                                 constraintName="fk_sanphachitiedotgiagia_on_dot_giam_gia" referencedColumnNames="id"
                                 referencedTableName="dot_giam_gia"/>
    </changeSet>
    <changeSet id="1748665161152-173" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="san_pham_chi_tiet_id" baseTableName="san_pham_chi_tiet_dot_giam_gia"
                                 constraintName="fk_sanphachitiedotgiagia_on_san_pham_chi_tiet"
                                 referencedColumnNames="id" referencedTableName="san_pham_chi_tiet"/>
    </changeSet>
    <changeSet id="1748665161152-174" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="danh_muc_id" baseTableName="san_pham_danh_muc"
                                 constraintName="fk_sanphadanmuc_on_danh_muc" referencedColumnNames="id"
                                 referencedTableName="danh_muc"/>
    </changeSet>
    <changeSet id="1748665161152-175" author="obscurites">
        <addForeignKeyConstraint baseColumnNames="san_pham_id" baseTableName="san_pham_danh_muc"
                                 constraintName="fk_sanphadanmuc_on_san_pham" referencedColumnNames="id"
                                 referencedTableName="san_pham"/>
    </changeSet>

</databaseChangeLog>