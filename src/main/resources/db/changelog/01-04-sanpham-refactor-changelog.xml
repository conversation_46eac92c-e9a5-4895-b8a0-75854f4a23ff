<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <!-- Phase 1: Add SKU field to san_pham_chi_tiet -->
    <changeSet id="add-sku-to-san-pham-chi-tiet" author="admin">
        <addColumn tableName="san_pham_chi_tiet">
            <column name="sku" type="VARCHAR(100)">
                <constraints nullable="true"/>
            </column>
        </addColumn>
        
        <!-- Add unique constraint for SKU -->
        <addUniqueConstraint 
            tableName="san_pham_chi_tiet" 
            columnNames="sku" 
            constraintName="uk_san_pham_chi_tiet_sku"/>
            
        <!-- Add index for SKU -->
        <createIndex tableName="san_pham_chi_tiet" indexName="idx_san_pham_chi_tiet_sku">
            <column name="sku"/>
        </createIndex>
    </changeSet>

    <!-- Phase 2: Migrate serial_number data to SKU field -->
    <changeSet id="migrate-serial-number-to-sku" author="admin">
        <sql>
            UPDATE san_pham_chi_tiet 
            SET sku = serial_number 
            WHERE sku IS NULL AND serial_number IS NOT NULL;
        </sql>
    </changeSet>

    <!-- Phase 3: Change trang_thai from enum to boolean -->
    <changeSet id="change-san-pham-chi-tiet-status-to-boolean" author="admin">
        <!-- Add new boolean column -->
        <addColumn tableName="san_pham_chi_tiet">
            <column name="trang_thai_new" type="BOOLEAN" defaultValueBoolean="true">
                <constraints nullable="false"/>
            </column>
        </addColumn>
        
        <!-- Migrate data: AVAILABLE -> true, others -> false -->
        <sql>
            UPDATE san_pham_chi_tiet 
            SET trang_thai_new = CASE 
                WHEN trang_thai = 'AVAILABLE' THEN true 
                ELSE false 
            END;
        </sql>
        
        <!-- Drop old column and rename new one -->
        <dropColumn tableName="san_pham_chi_tiet" columnName="trang_thai"/>
        <renameColumn tableName="san_pham_chi_tiet" 
                     oldColumnName="trang_thai_new" 
                     newColumnName="trang_thai"/>
    </changeSet>

    <!-- Phase 4: Remove reservation fields from san_pham_chi_tiet -->
    <changeSet id="remove-reservation-fields-from-san-pham-chi-tiet" author="admin">
        <dropColumn tableName="san_pham_chi_tiet" columnName="thoi_gian_dat_truoc"/>
        <dropColumn tableName="san_pham_chi_tiet" columnName="kenh_dat_truoc"/>
        <dropColumn tableName="san_pham_chi_tiet" columnName="don_hang_dat_truoc"/>
    </changeSet>

    <!-- Phase 5: Remove unused attribute columns -->
    <changeSet id="remove-unused-attribute-columns" author="admin">
        <!-- Remove foreign key constraints first -->
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_cong_giao_tiep"/>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_ban_phim"/>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_ket_noi_mang"/>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_am_thanh"/>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_webcam"/>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_bao_mat"/>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_he_dieu_hanh"/>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_pin"/>
        <dropForeignKeyConstraint baseTableName="san_pham_chi_tiet" constraintName="fk_san_pham_chi_tiet_thiet_ke"/>
        
        <!-- Remove unused attribute columns (keep only 6 core attributes) -->
        <dropColumn tableName="san_pham_chi_tiet" columnName="cong_giao_tiep_id"/>
        <dropColumn tableName="san_pham_chi_tiet" columnName="ban_phim_id"/>
        <dropColumn tableName="san_pham_chi_tiet" columnName="ket_noi_mang_id"/>
        <dropColumn tableName="san_pham_chi_tiet" columnName="am_thanh_id"/>
        <dropColumn tableName="san_pham_chi_tiet" columnName="webcam_id"/>
        <dropColumn tableName="san_pham_chi_tiet" columnName="bao_mat_id"/>
        <dropColumn tableName="san_pham_chi_tiet" columnName="he_dieu_hanh_id"/>
        <dropColumn tableName="san_pham_chi_tiet" columnName="pin_id"/>
        <dropColumn tableName="san_pham_chi_tiet" columnName="thiet_ke_id"/>
    </changeSet>

    <!-- Phase 6: Make SKU required after migration -->
    <changeSet id="make-sku-required" author="admin">
        <addNotNullConstraint tableName="san_pham_chi_tiet" columnName="sku"/>
    </changeSet>

    <!-- Phase 7: Remove serial_number column after migration to separate table -->
    <changeSet id="remove-serial-number-column" author="admin">
        <!-- Drop unique constraint first -->
        <dropUniqueConstraint tableName="san_pham_chi_tiet" constraintName="uk_san_pham_chi_tiet_serial_number"/>
        
        <!-- Remove the column -->
        <dropColumn tableName="san_pham_chi_tiet" columnName="serial_number"/>
    </changeSet>

    <!-- Phase 8: Add indexes for performance -->
    <changeSet id="add-performance-indexes" author="admin">
        <createIndex tableName="san_pham_chi_tiet" indexName="idx_san_pham_chi_tiet_san_pham_active">
            <column name="san_pham_id"/>
            <column name="trang_thai"/>
        </createIndex>
        
        <createIndex tableName="san_pham_chi_tiet" indexName="idx_san_pham_chi_tiet_price">
            <column name="gia_ban"/>
        </createIndex>
        
        <createIndex tableName="san_pham_chi_tiet" indexName="idx_san_pham_chi_tiet_core_attributes">
            <column name="cpu_id"/>
            <column name="ram_id"/>
            <column name="gpu_id"/>
            <column name="mau_sac_id"/>
            <column name="o_cung_id"/>
            <column name="man_hinh_id"/>
        </createIndex>
    </changeSet>

    <!-- Phase 9: Add comments for documentation -->
    <changeSet id="add-table-comments" author="admin">
        <sql>
            COMMENT ON TABLE san_pham_chi_tiet IS 'Product variants with specific configurations (CPU, RAM, GPU, Color, Storage, Screen Size)';
            COMMENT ON COLUMN san_pham_chi_tiet.sku IS 'Unique SKU for variant identification (e.g., MBA-M2-8GB-256GB-SILVER)';
            COMMENT ON COLUMN san_pham_chi_tiet.trang_thai IS 'Variant status (true=active, false=inactive)';
            COMMENT ON COLUMN san_pham_chi_tiet.gia_ban IS 'Regular selling price for this variant';
            COMMENT ON COLUMN san_pham_chi_tiet.gia_khuyen_mai IS 'Promotional price if applicable';
            COMMENT ON COLUMN san_pham_chi_tiet.cpu_id IS 'CPU specification reference';
            COMMENT ON COLUMN san_pham_chi_tiet.ram_id IS 'RAM specification reference';
            COMMENT ON COLUMN san_pham_chi_tiet.gpu_id IS 'GPU specification reference';
            COMMENT ON COLUMN san_pham_chi_tiet.mau_sac_id IS 'Color specification reference';
            COMMENT ON COLUMN san_pham_chi_tiet.o_cung_id IS 'Storage specification reference';
            COMMENT ON COLUMN san_pham_chi_tiet.man_hinh_id IS 'Screen size specification reference';
        </sql>
    </changeSet>

    <!-- Phase 10: Drop unused attribute tables -->
    <changeSet id="drop-unused-attribute-tables" author="admin">
        <!-- Drop tables for unused attributes -->
        <dropTable tableName="cong_giao_tiep"/>
        <dropTable tableName="ban_phim"/>
        <dropTable tableName="ket_noi_mang"/>
        <dropTable tableName="am_thanh"/>
        <dropTable tableName="webcam"/>
        <dropTable tableName="bao_mat"/>
        <dropTable tableName="he_dieu_hanh"/>
        <dropTable tableName="pin"/>
        <dropTable tableName="thiet_ke"/>
        
        <!-- Drop sequences for unused attributes -->
        <dropSequence sequenceName="cong_giao_tiep_id_seq"/>
        <dropSequence sequenceName="ban_phim_id_seq"/>
        <dropSequence sequenceName="ket_noi_mang_id_seq"/>
        <dropSequence sequenceName="am_thanh_id_seq"/>
        <dropSequence sequenceName="webcam_id_seq"/>
        <dropSequence sequenceName="bao_mat_id_seq"/>
        <dropSequence sequenceName="he_dieu_hanh_id_seq"/>
        <dropSequence sequenceName="pin_id_seq"/>
        <dropSequence sequenceName="thiet_ke_id_seq"/>
    </changeSet>

</databaseChangeLog>
